<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class UserDeleteRequestMail extends Mailable
{
    use Queueable, SerializesModels;

    private $payload;

    /**
     * Create a new message instance.
     */
    public function __construct(array $payload)
    {
        $this->payload = $payload;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            from: new Address(env('MAIL_FROM_ADDRESS'), config('mail.from.sd_name')),
            subject: $this->payload['subject'],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'Mails.DeletionRequestEmail',
            with: [
                'greeting' => $this->payload['greeting'],
                'body' => $this->payload['body'],
                'body2' => $this->payload['body2'] ?? '',
                'body3' => $this->payload['body3'] ?? '',
                'body4' => $this->payload['body4'] ?? '',
                'text' => $this->payload['text'],
                // 'link' => env(app.url . '/contact-us),
                'sender' => $this->payload['sender'],
                'userName' => $this->payload['userName'] ?? '',
                'domainName' => $this->payload['domainName'] ?? '',
                'reason' => $this->payload['reason'] ?? '',
                'additionalInfo' => $this->payload['additionalInfo'] ?? '',
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
