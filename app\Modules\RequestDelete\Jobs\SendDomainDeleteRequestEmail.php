<?php

namespace App\Modules\RequestDelete\Jobs;

use App\Mail\UserDeleteRequestMail;
use App\Events\EmailSentEvent;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Util\Constant\QueueConnection;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;
use Exception;
use Throwable;

class SendDomainDeleteRequestEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private array $data;
    private string $subject;
    private string $body;

    private string $body2;

    private string $body3;

    private string $body4;

    public $tries = 3;
    public $timeout = 120;

    /**
     * Create a new job instance.
     */
    public function __construct(array $data, string $subject, string $body, string $body2 = null, string $body3 = null, string $body4 = null)
    {
        $this->data = $data;
        $this->subject = $subject;
        $this->body = $body;
        $this->body2 = $body2;
        $this->body3 = $body3;
        $this->body4 = $body4;

        $this->onConnection(QueueConnection::EMAIL);
        $this->onQueue('default');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            if (!isset($this->data['userEmail']) || !isset($this->data['domainName'])) {
                app(AuthLogger::class)->error('SendDomainDeleteRequestEmail: Missing required data (userEmail or domainName)');
                return;
            }

            $message = [
                'subject'  => $this->subject,
                'greeting' => 'Greetings!',
                'body'     => $this->body,
                'body2'    => $this->body2 ?? '',
                'body3'    => $this->body3 ?? '',
                'body4'    => $this->body4 ?? '',
                'text'     => Carbon::now()->format('Y-m-d H:i:s'),
                'sender'   => 'StrangeDomains Support',
                'userName' => $this->data['userName'] ?? '',
                'domainName' => $this->data['domainName'] ?? '',
                'reason' => $this->data['reason'] ?? '',
                'additionalInfo' => $this->data['additionalInfo'] ?? '',
            ];

            Mail::to($this->data['userEmail'])->send(new UserDeleteRequestMail($message));

            event(new EmailSentEvent(
                $this->data['userId'],
                $this->data['userName'],
                $this->data['userEmail'],
                $this->subject,
                'Domain Deletion Request',
                json_encode($message),
                null
            ));

            app(AuthLogger::class)->info("Domain deletion request email sent successfully to {$this->data['userEmail']} for domain {$this->data['domainName']}");

        } catch (Exception $e) {
            app(AuthLogger::class)->error("Failed to send domain deletion request email to {$this->data['userEmail']}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(?Throwable $exception): void
    {
        app(AuthLogger::class)->error("SendDomainDeleteRequestEmail job failed for {$this->data['userEmail']}: " . $exception->getMessage());
    }

    /**
     * Get the backoff delays for retries.
     */
    public function backoff(): array
    {
        return [30, 60, 120];
    }
}
